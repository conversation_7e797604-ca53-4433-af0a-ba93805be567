import { <PERSON> } from "react-router-dom";
import { Navbar, Nav, NavDropdown, Container } from "react-bootstrap";

function AppNavbar() {
  return (
    <Navbar bg="dark" variant="dark" expand="lg">
      <Container>
        <Navbar.Brand as={Link} to="/">🎬 Movie App</Navbar.Brand>
        <Navbar.Toggle aria-controls="basic-navbar-nav" />
        <Navbar.Collapse id="basic-navbar-nav">
          <Nav className="me-auto">

            {/* Movies Dropdown */}
            <NavDropdown title="Movies" id="movies-dropdown">
              <NavDropdown.Item as={Link} to="/movies?category=now_playing">Now Playing</NavDropdown.Item>
              <NavDropdown.Item as={Link} to="/movies?category=popular">Popular</NavDropdown.Item>
              <NavDropdown.Item as={Link} to="/movies?category=top_rated">Top Rated</NavDropdown.Item>
              <NavDropdown.Item as={Link} to="/movies?category=upcoming">Upcoming</NavDropdown.Item>
            </NavDropdown>

            {/* Actors */}
            <Nav.Link as={Link} to="/actors">Actors</Nav.Link>

            {/* TV Shows Dropdown */}
            <NavDropdown title="TV Shows" id="tv-dropdown">
              <NavDropdown.Item as={Link} to="/tv?category=airing_today">Airing Today</NavDropdown.Item>
              <NavDropdown.Item as={Link} to="/tv?category=on_the_air">On TV</NavDropdown.Item>
              <NavDropdown.Item as={Link} to="/tv?category=popular">Popular</NavDropdown.Item>
              <NavDropdown.Item as={Link} to="/tv?category=top_rated">Top Rated</NavDropdown.Item>
            </NavDropdown>

          </Nav>
        </Navbar.Collapse>
      </Container>
    </Navbar>
  );
}

export default AppNavbar;
