import { Link } from "react-router-dom";

export default function MovieCard({ title, poster, rating, id }) {
  return (
    <div className="card h-100 shadow-sm">
      <img
        src={poster}
        className="card-img-top"
        alt={title}
        style={{ height: "350px", objectFit: "cover" }}
      />
      <div className="card-body d-flex flex-column">
        <h5 className="card-title text-truncate">{title}</h5>
        <p className="card-text">⭐ {rating}</p>
        <Link to={`/movie/${id}`} className="btn btn-primary mt-auto">
          View Details
        </Link>
      </div>
    </div>
  );
}
