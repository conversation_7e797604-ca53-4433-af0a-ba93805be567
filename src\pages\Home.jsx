import MovieCard from "../components/MovieCard";

export default function Home() {
  // بيانات الأفلام اللي إنت كنت باعتها
  const movies = [
    {
      id: 1,
      title: "The Shawshank Redemption",
      poster: "https://image.tmdb.org/t/p/w500/q6y0Go1tsGEsmtFryDOJo3dEmqu.jpg",
      rating: 9.3,
    },
    {
      id: 2,
      title: "The Godfather",
      poster: "https://image.tmdb.org/t/p/w500/eEslKSwcqmiNS6va24Pbxf2UKmJ.jpg",
      rating: 9.2,
    },
    {
      id: 3,
      title: "The Dark Knight",
      poster: "https://image.tmdb.org/t/p/w500/qJ2tW6WMUDux911r6m7haRef0WH.jpg",
      rating: 9.0,
    },
    {
      id: 4,
      title: "Interstellar",
      poster: "https://image.tmdb.org/t/p/w500/rAiYTfKGqDCRIIqo664sY9XZIvQ.jpg",
      rating: 8.6,
    },
  ];

  return (
    <div className="container my-4">
      <h2 className="mb-4">Trending Movies</h2>
      <div className="row g-4">
        {movies.map((movie) => (
          <div key={movie.id} className="col-12 col-sm-6 col-md-4 col-lg-3">
            <MovieCard
              title={movie.title}
              poster={movie.poster}
              rating={movie.rating}
            />
          </div>
        ))}
      </div>
    </div>
  );
}
