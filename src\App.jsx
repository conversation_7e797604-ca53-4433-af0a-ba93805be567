import React from "react";
import { Routes, Route } from "react-router-dom";
import Navbar from "./components/Navbar/Navbar";
import Navbar from "./components/Navbar";
import Home from "./pages/Home";
import Movies from "./pages/Movies/Movies";
import MovieDetails from "./pages/Movies/MovieDetails";
import Actors from "./pages/Actors/Actors";
import ActorDetails from "./pages/Actors/ActorDetails";
import TvShows from "./pages/TvShows/TvShows";
import TvShowDetails from "./pages/TvShows/TvShowDetails";

function App() {
  return (
    <>
      <Navbar />
      <div className="container mt-4">
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/movies" element={<Movies />} />
          <Route path="/movies/:id" element={<MovieDetails />} />
          <Route path="/actors" element={<Actors />} />
          <Route path="/actors/:id" element={<ActorDetails />} />
          <Route path="/tv" element={<TvShows />} />
          <Route path="/tv/:id" element={<TvShowDetails />} />
          <h1 className="text-center">Welcome to MyMovies 🎥</h1>
        </Routes>
      </div>
    </>
  );
}

export default App;
